
import React = require("react");
import { Items, UnknownItem } from "src/enums/resources";
import { ItemDescriptionPanel } from "./ItemDescriptionPanel";
import { createRoot } from "react-dom/client";
import { startProgressBar } from "src/gameinfo";
import { ItemIcon } from "./common";
import { getText } from "src/i18n";
import { ResourceMetaInfo } from "src/Interfaces";
import { FishingButton } from "./FishingButton";
import { useRootStore } from "../stores/rootStore";

const ResourceIcon = ({resource}) => {
    return (
        <div className={`resource-icon-inner ${resource.rarity}`}>
            <ItemIcon itemDef={resource} />
        </div>
    )
}

const ResourcesPanel = (props : {
    resourceMetaInfos: ResourceMetaInfo[],
}) => {
    const [selectedResource, setSelectedResource] = React.useState(null);

    // Check if resourceMetaInfos is undefined or null
    const metaInfos = props.resourceMetaInfos || [];

    return (
        <>
            <div className="resource-grid-container">
                <h3 className="resource-container">Available Resources:</h3>
                <div className="scrollable-container resource-grid">
                    {metaInfos.length === 0 && <p className="no-resources">No resources available in this area</p>}
                    {metaInfos.map((resourceMeta, index) => {
                        const resource = resourceMeta.discovered? Items[resourceMeta.id] : UnknownItem;
                        return (
                            <div key={index}
                                className={`resource-icon ${selectedResource?.id === resourceMeta.id ? 'selected' : ''}`}
                                onClick={() => setSelectedResource(resource)}>
                                <ResourceIcon resource={resource}/>
                            </div>
                        )
                    })}
                </div>
            </div>

            {selectedResource &&
                <div id="selectedResourceInfo">
                    <ItemDescriptionPanel itemDef={selectedResource} />
                </div>
            }
        </>
    )
};

export const EnvironmentDisplay = React.memo((props : {
    terrainName: string,
}) => {
    const {
        currRegionIndex,
        resourceMetaInfosInAllRegions,
        setResourceMetaInfosInAllRegions,
        addItemToInventory,
    } = useRootStore();
    const biomesList = useRootStore(state => state.biomesList);


    // Add null check to ensure resourceMetaInfosInAllRegions and currRegionIndex are valid
    const resourcesMetaInfo: ResourceMetaInfo[] =
        resourceMetaInfosInAllRegions &&
        currRegionIndex !== undefined &&
        currRegionIndex !== null &&
        resourceMetaInfosInAllRegions[currRegionIndex]
            ? resourceMetaInfosInAllRegions[currRegionIndex]
            : [];

    const exploreBiome = () => {
        if (!resourcesMetaInfo || resourcesMetaInfo.length === 0) {
            console.warn("No resources available to explore");
            return;
        }

        startProgressBar("Exploring", 60, () => {
            const discoveredResourceMeta = resourcesMetaInfo[Math.floor(Math.random() * resourcesMetaInfo.length)];
            // console.log("discoveredResourceMeta", discoveredResourceMeta);
            if (discoveredResourceMeta && !discoveredResourceMeta.discovered) {
                discoveredResourceMeta.discovered = true;
                setResourceMetaInfosInAllRegions([...resourceMetaInfosInAllRegions]);
            }

            if (discoveredResourceMeta && discoveredResourceMeta.id) {
                const discoveredResource = Items[discoveredResourceMeta.id];
                console.log("Exploring... discoveredResource", discoveredResource);
                if (discoveredResource) {
                    addItemToInventory(discoveredResource);
                }
            }
        })
    }

   return (
        <>
            {/* <div id="environmentDisplay"> */}
                <div id="terrainInfoDisplay">{"You are on " + biomesList[currRegionIndex]}</div>
                <div id="actionBtnsContainer">
                    <button id="exploreButton" onClick={exploreBiome}>
                        {getText("Explore This Area")}
                    </button>
                    <FishingButton />
                </div>
                <div id="resources-panel">
                    <ResourcesPanel
                        resourceMetaInfos={resourcesMetaInfo}
                    />
                </div>
            {/* </div> */}

            {/* <div id="inventoryDisplay">
                <InventoryPanel
                    inventorySize={20}
                />
            </div> */}

        </>
   );
});

