import { create } from 'zustand';

export type WindowType = 'Explore' | 'Character' | 'Craft' | 'Ground' | 'Inventory' | 'Equipment';

interface WindowState {
  position: { x: number, y: number };
  size: { width: number, height: number };
  isOpen: boolean;
  zIndex: number;
}

interface WindowStore {
  // Window states
  windows: Record<WindowType, WindowState>;
  highestZIndex: number;

  // Actions
  openWindow: (windowType: WindowType) => void;
  closeWindow: (windowType: WindowType) => void;
  setWindowPosition: (windowType: WindowType, position: { x: number, y: number }) => void;
  setWindowSize: (windowType: WindowType, size: { width: number, height: number }) => void;
  bringToFront: (windowType: WindowType) => void;
  toggleWindowVisibility: (windowType: WindowType) => void;
}

// Default positions for each window type
const defaultPositions: Record<WindowType, { x: number, y: number }> = {
  Explore: { x: 50, y: 50 },
  Character: { x: 100, y: 100 },
  Craft: { x: 150, y: 150 },
  Ground: { x: 200, y: 200 },
  Inventory: { x: 250, y: 250 },
  Equipment: { x: 300, y: 150 }
};

// Default sizes for each window type
const defaultSizes: Record<WindowType, { width: number, height: number }> = {
  Explore: { width: 400, height: 600 },
  Character: { width: 350, height: 450 },
  Craft: { width: 500, height: 400 },
  Ground: { width: 600, height: 500 },
  Inventory: { width: 590, height: 400 },
  Equipment: { width: 400, height: 500 }
};

// Create the store
export const useWindowStore = create<WindowStore>((set, get) => ({
  // Initial state
  windows: {
    Explore: { position: defaultPositions.Explore, size: defaultSizes.Explore, isOpen: false, zIndex: 1 },
    Inventory: { position: defaultPositions.Inventory, size: defaultSizes.Inventory, isOpen: false, zIndex: 5 },
    Craft: { position: defaultPositions.Craft, size: defaultSizes.Craft, isOpen: false, zIndex: 3 },
    Ground: { position: defaultPositions.Ground, size: defaultSizes.Ground, isOpen: false, zIndex: 4 },
    Equipment: { position: defaultPositions.Equipment, size: defaultSizes.Equipment, isOpen: false, zIndex: 6 },
    Character: { position: defaultPositions.Character, size: defaultSizes.Character, isOpen: false, zIndex: 2 },
  },
  highestZIndex: 6,

  // Actions
  openWindow: (windowType) => set((state) => {
    const newZIndex = state.highestZIndex + 1;
    return {
      windows: {
        ...state.windows,
        [windowType]: {
          ...state.windows[windowType],
          isOpen: true,
          zIndex: newZIndex
        }
      },
      highestZIndex: newZIndex
    };
  }),

  closeWindow: (windowType) => set((state) => ({
    windows: {
      ...state.windows,
      [windowType]: {
        ...state.windows[windowType],
        isOpen: false
      }
    }
  })),

  setWindowPosition: (windowType, position) => set((state) => ({
    windows: {
      ...state.windows,
      [windowType]: {
        ...state.windows[windowType],
        position
      }
    }
  })),

  setWindowSize: (windowType, size) => {
    console.log("setWindowSize", windowType, size);
    set((state) => ({
    windows: {
      ...state.windows,
      [windowType]: {
        ...state.windows[windowType],
        size
      }
    }
  }))
},

  bringToFront: (windowType) => set((state) => {
    const newZIndex = state.highestZIndex + 1;
    return {
      windows: {
        ...state.windows,
        [windowType]: {
          ...state.windows[windowType],
          zIndex: newZIndex
        }
      },
      highestZIndex: newZIndex
    };
  }),

  // Toggle window visibility - if closed, open it; if open, close it
  toggleWindowVisibility: (windowType) => set((state) => {
    const window = state.windows[windowType];
    const newZIndex = state.highestZIndex + 1;

    // If window is not open, open it
    if (!window.isOpen) {
      return {
        windows: {
          ...state.windows,
          [windowType]: {
            ...window,
            isOpen: true,
            zIndex: newZIndex
          }
        },
        highestZIndex: newZIndex
      };
    }

    // If window is already open, close it
    return {
      windows: {
        ...state.windows,
        [windowType]: {
          ...window,
          isOpen: false
        }
      }
    };
  })
}));
