import {
    initSettingsMenu, realSecToGameMinRatio, baseWaterDrainRate,
    baseEnergyDrainRate, baseFoodDrainRate, basePlayerSpeed
} from './settings.js';
import { TransitionsProbs, Weathers } from './weather.js';
import { randomNormDistribution } from './util.js';

import { gameStatus, player } from './gameinfo.js';
import { stopAudio, playSFX } from './settings.js';
import { getText, getTextWithArgs } from './i18n.js';


// ------------------- weather system -------------------
const weatherSystem = {
    current: 'clear',
    intensity: 0.5, // 0.0 to 1.0
    duration: 0, // remaining time in seconds
    transitionTime: 0, // time for weather to change
    element: null,
    // Weather probabilities based on previous weather
};

// Initialize weather UI
export function initWeatherUI() {
    // Create weather display
    const weatherDisplay = document.getElementById('weatherDisplay');
    weatherSystem.element = weatherDisplay;
    
    // Set initial weather
    setRandomWeather();
    updateWeatherDisplay();
}


// Set random weather based on transition probabilities
function setRandomWeather() {
    const currentWeather = weatherSystem.current;
    const transitions = TransitionsProbs[currentWeather];
    
    // Calculate cumulative probabilities
    const weatherTypes = Object.keys(transitions);
    const probabilities = weatherTypes.map(type => transitions[type]);
    
    let cumulativeProbability = 0;
    const cumulativeProbabilities = probabilities.map(p => cumulativeProbability += p);
    
    let newWeather = currentWeather; // Default to current weather
    
    // Generate random number and find corresponding weather
    const random = Math.random();
    for (let i = 0; i < cumulativeProbabilities.length; i++) {
        if (random <= cumulativeProbabilities[i]) {
            newWeather = weatherTypes[i];
            break;
        }
    }
    
    // Set new weather with random intensity and duration
    weatherSystem.current = newWeather;
    weatherSystem.intensity = 0.3 + Math.random() * 0.7; // 0.3 to 1.0
    weatherSystem.duration = randomNormDistribution(3, 4) * 60; // 60 + Math.random() * 180; // 3 to 24 hours
    weatherSystem.transitionTime = 10; // 10 seconds to transition
    
    const newWeatherObj = Weathers[newWeather];

    console.log("newWeather", newWeather);
    stopAudio();
    if (newWeatherObj.soundPath) {
        // playSFX('lightning');
        setTimeout(() => {
            playSFX(newWeatherObj.soundPath);
        }, 10);
    }

    // Modify drain rates
    if (newWeatherObj.playerEffects.waterDrain) {
        gameStatus.waterDrainRate = baseWaterDrainRate * (1 + (newWeatherObj.playerEffects.waterDrain - 1) * weatherSystem.intensity);
    }
    
    if (newWeatherObj.playerEffects.foodDrain) {
        gameStatus.foodDrainRate = baseFoodDrainRate * (1 + (newWeatherObj.playerEffects.foodDrain - 1) * weatherSystem.intensity);
    }
    
    if (newWeatherObj.playerEffects.energyDrain) {
        gameStatus.energyDrainRate = baseEnergyDrainRate * (1 + (newWeatherObj.playerEffects.energyDrain - 1) * weatherSystem.intensity);
    }

    // Modify movement speed
    if (newWeatherObj.playerEffects.moveSpeed) {
        player.speed = basePlayerSpeed * (1 + (newWeatherObj.playerEffects.moveSpeed - 1) * weatherSystem.intensity);
    } else {
        player.speed = basePlayerSpeed;
    }

    // Update display
    updateWeatherDisplay();
    
    // Apply weather overlay effect
    applyWeatherOverlay();
    
    console.log(`Weather changed to ${newWeatherObj.name} (${weatherSystem.intensity.toFixed(2)} intensity) for ${Math.round(weatherSystem.duration)} seconds`);
}

// Update weather display
function updateWeatherDisplay() {
    if (!weatherSystem.element) return;
    
    const weather = Weathers[weatherSystem.current];
    const intensity = Math.round(weatherSystem.intensity * 100);
    
    // Create weather icon and name container
    const mainContent = document.createElement('div');
    mainContent.style.cssText = 'display: flex; align-items: center; gap: 8px;';
    
    // Weather icon
    const weatherIcon = document.createElement('div');
    weatherIcon.style.cssText = 'font-size: 14px;';
    weatherIcon.textContent = weather.icon;
    
    // Weather name and info icon container
    const nameContainer = document.createElement('div');
    nameContainer.style.cssText = 'display: flex; align-items: center;';
    
    const weatherName = document.createElement('div');
    weatherName.style.cssText = 'font-weight: bold;';
    weatherName.textContent = weather.name;
    
    // Create info icon
    const infoIcon = document.createElement('div');
    infoIcon.className = 'weather-info-icon';
    infoIcon.innerHTML = '<img src = "images/info.svg"/>';
    infoIcon.style.cssText = `
        margin-left: 6px;
        display: flex;
        filter: invert(1);
    `;

    let popover = null;
    infoIcon.addEventListener('click', (e) => {
        e.stopPropagation();
        
        // Remove existing popover if any
        if (popover) {
            document.body.removeChild(popover);
            popover = null;
            return;
        }

        // Create popover
        popover = document.createElement('div');
        popover.className = 'item-description-popover';
        
        // Create content wrapper for proper sizing
        const content = document.createElement('div');
        content.style.cssText = 'display: inline-block; padding: 8px 12px; white-space: nowrap;';
        content.innerHTML = `${weather.description}<br><br>${getWeatherEffectsDescription(weather)}`;
        popover.appendChild(content);
        
        // Add to DOM temporarily to get dimensions
        popover.style.cssText = `
            visibility: hidden;
            position: fixed;
            width: auto;
            height: auto;
            padding: 0;
            z-index: 9999;
            min-width: fit-content;
        `;
        document.body.appendChild(popover);
        
        // Get dimensions
        const rect = infoIcon.getBoundingClientRect();
        const popoverRect = popover.getBoundingClientRect();
        
        // Calculate positions for different sides
        const positions = {
            bottom: {
                top: rect.bottom + 10,
                left: rect.left - (popoverRect.width / 2) + (rect.width / 2)
            },
            top: {
                top: rect.top - popoverRect.height - 10,
                left: rect.left - (popoverRect.width / 2) + (rect.width / 2)
            },
            right: {
                top: rect.top + (rect.height / 2) - (popoverRect.height / 2),
                left: rect.right + 10
            },
            left: {
                top: rect.top + (rect.height / 2) - (popoverRect.height / 2),
                left: rect.left - popoverRect.width - 10
            }
        };

        // Check which position has the most space
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        
        let bestPosition = 'bottom';
        let arrowClass = 'arrow-top';

        // Check if bottom position would go off-screen
        if (positions.bottom.top + popoverRect.height > windowHeight) {
            if (positions.top.top > 0) {
                bestPosition = 'top';
                arrowClass = 'arrow-bottom';
            } else if (positions.right.left + popoverRect.width < windowWidth) {
                bestPosition = 'right';
                arrowClass = 'arrow-left';
            } else if (positions.left.left > 0) {
                bestPosition = 'left';
                arrowClass = 'arrow-right';
            }
        }

        // Ensure popover stays within window bounds
        const finalPosition = positions[bestPosition];
        finalPosition.left = Math.max(10, Math.min(windowWidth - popoverRect.width - 10, finalPosition.left));
        finalPosition.top = Math.max(10, Math.min(windowHeight - popoverRect.height - 10, finalPosition.top));

        // Apply final position and show popover
        popover.style.top = `${finalPosition.top}px`;
        popover.style.left = `${finalPosition.left}px`;
        popover.className = `item-description-popover ${arrowClass}`;
        
        // Add arrow
        const arrow = document.createElement('div');
        arrow.className = 'popover-arrow';
        
        // Position arrow based on available space
        if (bestPosition === 'bottom' || bestPosition === 'top') {
            const arrowLeft = rect.left - finalPosition.left + (rect.width / 2);
            arrow.style.left = `${arrowLeft}px`;
        } else {
            const arrowTop = rect.top - finalPosition.top + (rect.height / 2);
            arrow.style.top = `${arrowTop}px`;
        }
        
        popover.appendChild(arrow);
        popover.style.visibility = 'visible';

        // Close popover when clicking outside
        const closePopover = (e) => {
            if (!popover.contains(e.target) && e.target !== infoIcon) {
                document.body.removeChild(popover);
                popover = null;
                document.removeEventListener('click', closePopover);
            }
        };
        setTimeout(() => document.addEventListener('click', closePopover), 0);
    });

    nameContainer.appendChild(weatherName);
    nameContainer.appendChild(infoIcon);
    
    mainContent.appendChild(weatherIcon);
    mainContent.appendChild(nameContainer);
    
    // Clear previous content and add new
    weatherSystem.element.innerHTML = '';
    weatherSystem.element.appendChild(mainContent);
}

// Get description of weather
function getWeatherEffectsDescription(weather) {
    let description = '';
    
    if (Object.keys(weather.playerEffects).length > 0) {
        description += getText('weather_effects_title') + '<br>';
        for (const [effect, value] of Object.entries(weather.playerEffects)) {
            let effectName = '';
            let effectDesc = '';
            
            switch (effect) {
                case 'waterDrain':
                    effectName = getText('weather_thirst_rate');
                    effectDesc = value < 1 
                        ? getTextWithArgs('weather_effect_slower', { value: Math.round((1-value)*100) })
                        : getTextWithArgs('weather_effect_faster', { value: Math.round((value-1)*100) });
                    break;
                case 'foodDrain':
                    effectName = getText('weather_hunger_rate');
                    effectDesc = value < 1 
                        ? getTextWithArgs('weather_effect_slower', { value: Math.round((1-value)*100) })
                        : getTextWithArgs('weather_effect_faster', { value: Math.round((value-1)*100) });
                    break;
                case 'energyDrain':
                    effectName = getText('weather_energy_consumption');
                    effectDesc = value < 1 
                        ? getTextWithArgs('weather_effect_slower', { value: Math.round((1-value)*100) })
                        : getTextWithArgs('weather_effect_faster', { value: Math.round((value-1)*100) });
                    break;
                case 'moveSpeed':
                    effectName = getText('weather_movement_speed');
                    effectDesc = value < 1 
                        ? getTextWithArgs('weather_effect_slower', { value: Math.round((1-value)*100) })
                        : getTextWithArgs('weather_effect_faster', { value: Math.round((value-1)*100) });
                    break;
                case 'healthRisk':
                    effectName = getText('weather_health_risk');
                    effectDesc = getTextWithArgs('weather_effect_health_risk', { value: Math.round(value*100) });
                    break;
            }
            
            description += `- ${effectName}: ${effectDesc}<br>`;
        }
    }
    
    if (Object.keys(weather.resourceEffects).length > 0) {
        description += '<br>' + getText('weather_resource_effects_title') + '<br>';
        for (const [effect, value] of Object.entries(weather.resourceEffects)) {
            let effectName = '';
            let effectDesc = '';
            
            switch (effect) {
                case 'gatherSpeed':
                    effectName = getText('weather_gathering_speed');
                    effectDesc = value < 1 
                        ? getTextWithArgs('weather_effect_slower', { value: Math.round((1-value)*100) })
                        : getTextWithArgs('weather_effect_faster', { value: Math.round((value-1)*100) });
                    break;
                case 'berryChance':
                    effectName = getText('weather_berry_find_rate');
                    effectDesc = value < 1 
                        ? getTextWithArgs('weather_effect_lower', { value: Math.round((1-value)*100) })
                        : getTextWithArgs('weather_effect_higher', { value: Math.round((value-1)*100) });
                    break;
                case 'mushroomChance':
                    effectName = getText('weather_mushroom_find_rate');
                    effectDesc = value < 1 
                        ? getTextWithArgs('weather_effect_lower', { value: Math.round((1-value)*100) })
                        : getTextWithArgs('weather_effect_higher', { value: Math.round((value-1)*100) });
                    break;
            }
            
            description += `- ${effectName}: ${effectDesc}<br>`;
        }
    }
    
    return description;
}

// Update the applyWeatherOverlay function to add rain animation
function applyWeatherOverlay() {
    // Remove existing overlay
    const existingOverlay = document.getElementById('weather-overlay');
    if (existingOverlay) {
        document.body.removeChild(existingOverlay);
    }
    
    // Create new overlay
    const weather = Weathers[weatherSystem.current];
    const overlay = document.createElement('div');
    overlay.id = 'weather-overlay';
    
    // Set base style
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 9;
        opacity: 0;
        transition: opacity ${weatherSystem.transitionTime}s;
        background: ${weather.color};
    `;
    
    // Add weather-specific effects
    switch (weatherSystem.current) {
        case 'rainy':
            // Create rain container
            const rainContainer = document.createElement('div');
            rainContainer.className = 'rain-container';
            rainContainer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
            `;
            
            // Create multiple rain layers for depth effect
            const rainCount = Math.floor(20 * weatherSystem.intensity);
            for (let i = 0; i < rainCount; i++) {
                const drop = document.createElement('div');
                drop.className = 'rain-drop';
                
                // Randomize drop properties
                const size = 0.5 + Math.random() * 1.5;
                const opacity = 0.3 + Math.random() * 0.7;
                const left = Math.random() * 100;
                const animationDuration = 0.5 + Math.random() * 0.5;
                const delay = Math.random();
                
                drop.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${10 + size * 10}px;
                    background: rgba(200, 230, 255, ${opacity});
                    left: ${left}%;
                    top: -20px;
                    animation: rainFall ${animationDuration}s linear ${delay}s infinite;
                `;
                
                rainContainer.appendChild(drop);
            }
            
            overlay.appendChild(rainContainer);
            break;
            
        case 'stormy':
            // Create storm container with heavier rain and lightning
            const stormContainer = document.createElement('div');
            stormContainer.className = 'storm-container';
            stormContainer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
            `;
            
            // Create heavier rain
            const stormDropCount = Math.floor(40 * weatherSystem.intensity);
            for (let i = 0; i < stormDropCount; i++) {
                const drop = document.createElement('div');
                drop.className = 'storm-drop';
                
                // Randomize drop properties
                const size = 1 + Math.random() * 2;
                const opacity = 0.4 + Math.random() * 0.6;
                const left = Math.random() * 100;
                const animationDuration = 0.3 + Math.random() * 0.4;
                const delay = Math.random() * 3;
                const angle = -10 - Math.random() * 20; // Angled rain
                
                drop.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${15 + size * 15}px;
                    background: rgba(200, 230, 255, ${opacity});
                    left: ${left}%;
                    top: -30px;
                    transform: rotate(${angle}deg);
                    animation: stormFall ${animationDuration}s linear ${delay}s infinite;
                `;
                
                stormContainer.appendChild(drop);
            }
            
            // Add occasional lightning flash
            const lightningContainer = document.createElement('div');
            lightningContainer.className = 'lightning-container';
            lightningContainer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: white;
                opacity: 0;
                animation: lightningFlash ${10 + Math.random() * 20}s ease-out infinite;
            `;
            
            stormContainer.appendChild(lightningContainer);
            overlay.appendChild(stormContainer);
            break;
            
        case 'foggy':
            overlay.style.animation = 'fogAnimation 6s infinite';
            break;
    }
    
    document.body.appendChild(overlay);
    
    // Fade in the overlay
    setTimeout(() => {
        overlay.style.opacity = weatherSystem.intensity * 0.7; // Max opacity 0.7
    }, 10);
}

// Update weather (call this from game loop)
export function updateWeather(inGameMin) {
    // Decrease duration
    weatherSystem.duration -= inGameMin;
    
    // Change weather if duration is up
    if (weatherSystem.duration <= 0) {
        setRandomWeather();
    }
    
    // Apply weather effects to player
    applyWeatherEffects(inGameMin);
}

// Apply weather effects to player
function applyWeatherEffects(inGameMin) {
    const weather = Weathers[weatherSystem.current];
    const intensity = weatherSystem.intensity;
    
    if (weather.playerEffects) {

        // // Modify drain rates
        // if (weather.playerEffects.waterDrain) {
        //     gameStatus.waterDrainRate = baseWaterDrainRate * (1 + (weather.playerEffects.waterDrain - 1) * intensity);
        // }
        
        // if (weather.playerEffects.foodDrain) {
        //     gameStatus.foodDrainRate = baseFoodDrainRate * (1 + (weather.playerEffects.foodDrain - 1) * intensity);
        // }
        
        // if (weather.playerEffects.energyDrain) {
        //     gameStatus.energyDrainRate = baseEnergyDrainRate * (1 + (weather.playerEffects.energyDrain - 1) * intensity);
        // }
        
        // Apply health risk (e.g., lightning strikes during storms)
        if (weather.playerEffects.healthRisk
            && Math.random() < weather.playerEffects.healthRisk * intensity * (inGameMin / 60)) {
            // Lightning strike!
            gameStatus.health -= 10;
            const bar = document.getElementById(`health-bar`);
            if (bar) {
                bar.style.width = `${gameStatus.health}%`;
            }
            
            // Visual effect
            const flash = document.createElement('div');
            flash.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: white;
                opacity: 0.8;
                pointer-events: none;
                z-index: 9999;
            `;
            document.body.appendChild(flash);
            
            // Fade out
            setTimeout(() => {
                flash.style.transition = 'opacity 0.5s';
                flash.style.opacity = 0;
                setTimeout(() => document.body.removeChild(flash), 500);
            }, 100);
            
        }
    }
}
