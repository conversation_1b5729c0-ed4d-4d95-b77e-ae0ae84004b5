import { getText } from "src/i18n";
import { FISH } from "./fishing_enums";
import { RARITY, ResourceTypes } from "./common_enum";
// import { MATERIALS } from "./Materials";
import { Meat, Sweetener } from "src/Interfaces";
import { MATERIALS } from "./Materials";
import { MEAT } from "./animal_enums";


export const CRAFTABLE_FOODS = {
    // "Healing Potion": {
    //     id: 'Healing Potion',
    //     name: 'Healing Potion',
    //     icon: '🧪',
    //     type: ResourceTypes.MEDICINAL,
    //     description: 'A potent healing mixture',
    //     ingredients: [
    //         { id: 'Red Berries', quantity: 2 },
    //         { id: 'Herbs', quantity: 1 }
    //     ],
    //     health: 30
    // },
    // "Juice": {
    //     id: 'Juice',
    //     name: 'Juice',
    //     icon: 'images/juice.svg',
    //     type: ResourceTypes.EDIBLE,
    //     description: 'A refreshing beverage',
    //     ingredients: [
    //         { itemDef: MATERIALS.Water, quantity: 1 },
    //     ],
    //     food: 15,
    //     water: 20,
    //     energy: 2
    // },
};

export const WATER = {
    id: "Water",
    get name() { return getText("Water"); },
    get description() { return getText("desc_water"); },
    "type": ResourceTypes.EDIBLE,
    "icon": "💧",
    "rarity": RARITY.COMMON,
    "water": 10,
    waterVal: 2
};

export const ICE = {
    id: "Ice",
    get name() { return getText("Ice"); },
    get description() { return getText("desc_Ice"); },
    "type": ResourceTypes.EDIBLE,
    "icon": "💧",
    "rarity": RARITY.COMMON,
    "water": 10,
    waterVal: 2
};

export const FRUITS = {
    "Pineapple": {
        "id": "Pineapple",
        get name() { return getText("Pineapple"); },
        get description() { return getText("Pineapple"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🍍",
        "rarity": RARITY.LEGENDARY,
        "food": 15,
        "water": 5,
        "energy": 2,
        "health": 1,
        "fruitVal": 2,
    },
    "Apple": {
        id: "Apple",
        get name() { return getText("Apple"); },
        get description() { return getText("desc_apple"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "images/apple.svg",
        "rarity": RARITY.LEGENDARY,
        "food": 10,
        "water": 1,
        "energy": 1,
        "health": 1,
        fruitVal: 1,
        waterVal: 0.5
    },
    "Coconut": {
        id: "Coconut",
        get name() { return getText("Coconut"); },
        get description() { return getText("desc_coconut"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥥",
        "rarity": RARITY.UNCOMMON,
        "food": 20,
        "water": 1,
        "energy": 1,
        "health": 1,
        foodType: "fruit",
        fruitValue: 0.5,
        waterVal: 2
    },
    "Banana": {
        id: "Banana",
        get name() { return getText("Banana"); },
        get description() { return getText("desc_Banana"); },
        "type": ResourceTypes.EDIBLE,
        "rarity": RARITY.EPIC,
        "icon": "🍌",
        "food": 20,
        "water": 5,
        "energy": 25,
        "health": 4,
        fruitVal: 0.5
    },
    "BlueBerry": {
        id: "BlueBerry",
        get name() { return getText("BlueBerry"); },
        get description() { return getText("desc_BlueBerry"); },
        "type": ResourceTypes.EDIBLE,
        "rarity": RARITY.EPIC,
        "icon": "🫐",
        "food": 10,
        "water": 5,
        "energy": 15,
        "health": 2,
        fruitVal: 0.1,
        waterVal: 0.1
    },
    "RosehipBerry": {
        "id": "RosehipBerry",
        "name": "Rosehip Berry",
        "type": ResourceTypes.EDIBLE,
        "icon": "🍒",
        "rarity": RARITY.UNCOMMON,
        "food": 5,
        "water": 2,
        "health": 3,
        "fruitVal": 0.2,
        get description() { return getText("Rosehip Berry"); }
    },

    "Mango": {
        id: "Mango",
        get name() { return getText("Mango"); },
        get description() { return getText("desc_"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥭",
        "rarity": RARITY.LEGENDARY,
        "food": 25,
        "water": 10,
        "energy": 20,
        "health": 5,
        fruitVal: 1,
        waterVal: 0.5
    },
    "Papaya": {
        "id": "Papaya",
        "name": "Papaya",
        "type": ResourceTypes.EDIBLE,
        "icon": "🥭",
        "rarity": RARITY.UNCOMMON,
        "food": 12,
        "water": 8,
        "energy": 6,
        "health": 3,
        "fruitVal": 1,
        get description() { return getText("Papaya"); }
    },
    "BeachPlum": {
        id: "BeachPlum",
        get name() { return getText("BeachPlum"); },
        get description() { return getText("desc_BeachPlum"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🍇",
        "rarity": RARITY.RARE,
        "food": 1,
        "water": 1,
        "health": 1,
        fruitVal: 0.5,
    },
    "Grape": {
        id: "Grape",
        get name() { return getText("Grape"); },
        get description() { return getText("desc_Grape"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🍇",
        "rarity": RARITY.RARE,
        "food": 1,
        "water": 1,
        "health": 1,
        fruitVal: 0.1,
    }
};

export const EGGS = {
    "PoultryEgg": {
        id: "PoultryEgg",
        get name() { return getText("PoultryEgg"); },
        get description() { return getText("desc_PoultryEgg"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥚",
        "rarity": RARITY.COMMON,
        "food": 5,
        "health": 1,
        eggVal: 1,
    },
    "CookedPoultryEgg": {
        id: "CookedPoultryEgg",
        get name() { return getText("CookedPoultryEgg"); },
        get description() { return getText("desc_CookedPoultryEgg"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥚",
        "rarity": RARITY.COMMON,
        "food": 6,
        eggVal: 1,
    },
    "TurtleEgg": {
        "id": "TurtleEgg",
        get name() { return getText("TurtleEgg"); },
        get description() { return getText("Turtle Egg"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥚",
        "rarity": RARITY.RARE,
        "food": 3,
        "energy": 1,
        "health": 1,
    },
};

export const NUTS = {
    "Acorn": {
        id: "Acorn",
        get name() { return getText("Acorn"); },
        get description() { return getText("desc_acorn"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "images/acorn.png",
        "rarity": RARITY.COMMON,
        "food": 5,
        "quantityMod": 2,
        nutVal: 1
    },
    "Peanut": {
        id: "Peanut",
        get name() { return getText("Peanut"); },
        get description() { return getText("desc_peanut"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥜",
        "rarity": RARITY.COMMON,
        "food": 20,
        "energy": 1,
        "health": 1,
        foodType: "nut",
        freshness: 100,
        nutVal: 1,
    },
};

export const VEGETABLES = {
    "Seaweed": {
        id: "Seaweed",
        get name() { return getText("item_seaweed"); },
        get description() { return getText("desc_seaweed"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.COMMON,
        "freshness": 10,
        "icon": "🌿",
        "food": 2,
        "water": 5,
        waterVal: 0.5
    },
    "Carrot": {
        id: "Carrot",
        get name() { return getText("Carrot"); },
        get description() { return getText("desc_Carrot"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.EPIC,
        "freshness": 20,
        "icon": "🥕",
        "food": 2,
        "water": 1,
        "health": 1,
        vegVal: 1,
    },
    "Potato": {
        "id": "Potato",
        get name() { return getText("Potato"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥔",
        "rarity": RARITY.LEGENDARY,
        "food": 12,
        "energy": 8,
        get description() { return getText("Potato"); }
    },
}

export const SWEETENERS: { [key: string]: Sweetener } = {
    "Honey": {
        id: "Honey",
        get name() { return getText("Honey"); },
        get description() { return getText("desc_"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🍯",
        "food": 5,
        "energy": 1,
        "health": 1,
        sweetVal: 2
    },
    "MapleSap": {
        id: "MapleSap",
        get name() { return getText("MapleSap"); },
        get description() { return getText("desc_MapleSap"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "images/maple_sap.svg",
        sweetVal: 1,
        waterVal: 0.5
    }
};

export const DIARY = {
    "Milk": {
        id: "Milk",
        get name() { return getText("Milk"); },
        get description() { return getText("desc_"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥛",
        "food": 2,
        "water": 5,
        "energy": 1,
        "health": 1,
        diaryVal: 1
    },
};

export const MUSHROOMS = {
    "ButtonMushroom": {
        id: "ButtonMushroom",
        get name() { return getText("ButtonMushroom"); },
        get description() { return getText("desc_ButtonMushroom"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 3,
        mushroomVal: 0.5,
        freshness: 3
    },
    "CookedButtonMushroom": {
        id: "ButtonMushroom",
        get name() { return getText("CookedButtonMushroom"); },
        get description() { return getText("desc_CookedButtonMushroom"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 5,
        mushroomVal: 0.5,
        freshness: 5
    },
    "OysterMushroom": {
        id: "OysterMushroom",
        get name() { return getText("OysterMushroom"); },
        get description() { return getText("desc_OysterMushroom"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 6,
        mushroomVal: 1,
        freshness: 2
    },
    "CookedOysterMushroom": {
        id: "OysterMushroom",
        get name() { return getText("CookedOysterMushroom"); },
        get description() { return getText("desc_CookedOysterMushroom"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 6,
        health: 2,
        mushroomVal: 1,
        freshness: 4
    },
    "Chanterelle": {
        id: "Chanterelle",
        get name() { return getText("Chanterelle"); },
        get description() { return getText("desc_Chanterelle"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.LEGENDARY,
        "icon": "🍄",
        "food": 10,
        health: -10,
        mushroomVal: 1,
        quantityMod: 2,
        quantityModMap: {
            "Taiga": 1
        },
        freshness: 1
    },
    "CookedChanterelle": {
        id: "CookedChanterelle",
        get name() { return getText("CookedChanterelle"); },
        get description() { return getText("desc_CookedChanterelle"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.LEGENDARY,
        "icon": "🍄",
        "food": 10,
        health: 2,
        mushroomVal: 1,
    },
    "Morel": {
        id: "Morel",
        get name() { return getText("Morel"); },
        get description() { return getText("desc_Morel"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "images/morel.png",
        "food": 5,
        health: -10,
        mushroomVal: 1
    },
    "CookedMorel": {
        id: "CookedMorel",
        get name() { return getText("CookedMorel"); },
        get description() { return getText("desc_CookedMorel"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 6,
        mushroomVal: 1
    },
    "ShiitakeMushroom": {
        id: "ShiitakeMushroom",
        get name() { return getText("ShiitakeMushroom"); },
        get description() { return getText("desc_ShiitakeMushroom"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 5,
        health: 3,
        mushroomVal: 1
    },
    "FlyAgaric": {
        id: "FlyAgaric",
        get name() { return getText("FlyAgaric"); },
        get description() { return getText("desc_FlyAgaric"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 5,
        "health": -15,
        mushroomVal: 1
    },
    "FalseMorel": {
        id: "FalseMorel",
        get name() { return getText("FalseMorel"); },
        get description() { return getText("desc_FalseMorel"); },
        "type": ResourceTypes.EDIBLE,
        rarity: RARITY.RARE,
        "icon": "🍄",
        "food": 5,
        "health": -15,
        mushroomVal: 1
    }

}


export const COOKABLE_FOODS = {
    "VegetableSoup": {
        id: "VegetableSoup",
        get name() { return getText("VegetableSoup"); },
        get description() { return getText("desc_VegetableSoup"); },
        type: ResourceTypes.EDIBLE,
        icon: "🍲",
        food: 30,
        water: 15,
        energy: 20,
        health: 10,
        requiredWaterVal: 2,
        requiredVegVal: 3,
        priority: 1,
        discovered: false
    },
    "FruitSalad": {
        id: "FruitSalad",
        get name() { return getText("FruitSalad"); },
        get description() { return getText("desc_FruitSalad"); },
        type: ResourceTypes.EDIBLE,
        icon: "🥗",
        food: 25,
        water: 20,
        energy: 30,
        health: 15,
        requiredFruitVal: 3,
        priority: 1,
        discovered: false
    },
    "MeatStew": {
        id: "MeatStew",
        get name() { return getText("MeatStew"); },
        get description() { return getText("desc_MeatStew"); },
        type: ResourceTypes.EDIBLE,
        icon: "🍖",
        food: 50,
        water: 10,
        energy: 40,
        health: 20,
        requiredWaterVal: 1,
        requiredMeatVal: 3,
        priority: 1,
        discovered: false
    },
    "FishSoup": {
        id: "FishSoup",
        get name() { return getText("FishSoup"); },
        get description() { return getText("desc_FishSoup"); },
        type: ResourceTypes.EDIBLE,
        icon: "🐟",
        food: 40,
        water: 15,
        energy: 30,
        health: 15,
        requiredWaterVal: 2,
        requiredFishVal: 2,
        priority: 1,
        discovered: false
    },
    "SweetFruitCompote": {
        id: "SweetFruitCompote",
        get name() { return getText("SweetFruitCompote"); },
        get description() { return getText("desc_SweetFruitCompote"); },
        type: ResourceTypes.EDIBLE,
        icon: "🍯",
        food: 35,
        water: 15,
        energy: 50,
        health: 10,
        requiredFruitVal: 2,
        requiredSweetVal: 1,
        priority: 3,
        discovered: false
    },
    "MeatAndVegetableStew": {
        id: "MeatAndVegetableStew",
        get name() { return getText("MeatAndVegetableStew"); },
        get description() { return getText("desc_MeatAndVegetableStew"); },
        type: ResourceTypes.EDIBLE,
        icon: "🍲",
        food: 60,
        water: 20,
        energy: 50,
        health: 25,
        requiredWaterVal: 2,
        requiredMeatVal: 2,
        requiredVegVal: 2,
        priority: 1,
        discovered: false
    },
    "VegetableAndFishStew": {
        id: "VegetableAndFishStew",
        get name() { return getText("VegetableAndFishStew"); },
        get description() { return getText("desc_VegetableAndFishStew"); },
        type: ResourceTypes.EDIBLE,
        icon: "🍲",
        food: 55,
        water: 20,
        energy: 40,
        health: 20,
        requiredWaterVal: 2,
        requiredVegVal: 2,
        requiredFishVal: 2,
        priority: 1,
        discovered: false
    },
    "IceCream": {
        id: "IceCream",
        get name() { return getText("IceCream"); },
        get description() { return getText("desc_IceCream"); },
        type: ResourceTypes.EDIBLE,
        icon: "🍨",
        food: 30,
        water: 10,
        energy: 20,
        health: 10,
        ingredients: [
            { itemDef: ICE, quantity: 1 },
            { itemDef: DIARY.Milk, quantity: 1 }
        ],
        requiredWaterVal: 1,
        requiredSweetVal: 2,
        priority: 4,
        discovered: false
    },
    "BananaSplit": {
        id: "BananaSplit",
        get name() { return getText("BananaSplit"); },
        get description() { return getText("desc_BananaSplit"); },
        type: ResourceTypes.EDIBLE,
        icon: "🍌",
        food: 40,
        water: 10,
        energy: 30,
        health: 10,
        ingredients: [
            { itemDef: FRUITS.Banana, quantity: 1 },
            { itemDef: ICE, quantity: 1 },
            { itemDef: DIARY.Milk, quantity: 1 }
        ],
        requiredFruitVal: 2,
        requiredWaterVal: 1,
        requiredSweet: 2,
        priority: 5,
        discovered: false
    },
    "MysterySoup": {
        id: "MysterySoup",
        get name() { return getText("MysterySoup"); },
        get description() { return getText("desc_MysterySoup"); },
        type: ResourceTypes.EDIBLE,
        icon: "🥣",
        food: 15,
        water: 10,
        energy: 10,
        health: 5,
        priority: 0,
        discovered: true // This is always known as a fallback
    }
}


export const FOOD = {
    ...EGGS,
    ...DIARY,
    "Water": WATER,
    "Ice": ICE,
    ...COOKABLE_FOODS,
    ...FRUITS,
    ...FISH,
    ...VEGETABLES,
    ...NUTS,
    ...MEAT,
    ...SWEETENERS,
    ...MUSHROOMS,
    "Rot": {
        "name": "Rot",
        "type": ResourceTypes.EDIBLE,
        "icon": "☠️",
        "food": 2,
        "water": 1 ,
        "energy": -5,
        "health": -30
    },
};

