
<div id="window-container">

    <div class="transparent-window" id="Environment">
      <div id="environmentDisplay">
        <div id="terrainInfoDisplay"></div>
        <div id="actionBtnsContainer">
          <button id="exploreButton">Explore this area</button>
          <!-- <button id="huntButton">Hunt</button> -->
        </div>
        <div id="resources-panel"></div>
        <!-- <div class="resource-grid-container"></div> -->
        <!-- <div id="selectedResourceInfo"></div> -->
        <!-- <button id="gatherButton">Gather</button> -->
      </div>

      <div id="inventoryDisplay">
      </div>
    </div>

    <div class="transparent-window" id="Equipment">
      <!-- 内容2 -->
    </div>

    <div class="transparent-window" id="Craft">
        <div id="recipeList">
          <div style="font-weight: bold; margin-bottom: 8px;">Available Recipes</div>
          <div id="recipeCategoryTabs" class="recipe-tabs"></div>
          <div id="recipeListContent" class="scrollable-container"></div>
        </div>
        <div id="detailsPanel">
          <h3>Recipe Details</h3>
          <p>Select a recipe to view details</p>
          <button id="craftButton">
            Craft Item
          </button>
        </div>
    </div>

    <div class="transparent-window" id="Building">
      
    </div>

  </div>
  <div class="button-container">
    <button class="switch-button active">Explore</button>
    <button class="switch-button">Equipment</button>
    <button class="switch-button">Craft</button>
    <button class="switch-button">Ground</button>
  </div>