import { getText } from "src/i18n";
import { RARITY, ResourceTypes } from "./common_enum";
import { MATERIALS } from "./Materials";
import { FISHING_RODS } from "./fishing_enums";
import { BackPack, CraftableItem, CraftableTool, EquipableItem, SlotType } from "src/Interfaces";
import { Boats } from "./boat_enums";
interface EquipmentSlotTypes {
    BACKPACK: SlotType;
    HEAD: SlotType;
    BODY: SlotType;
    HAND: SlotType;
    FEET: SlotType;
}

const EQUIPMENT_SLOT_TYPES: EquipmentSlotTypes = {
    HEAD: {
        id: 'HEAD',
        get name() { return getText("equipable_slot_head"); },
        icon: "🧢",
        get description() { return getText("equipable_slot_head_desc"); },
    },
    BODY: {
        id: 'BODY',
        get name() { return getText("equipable_slot_body"); },
        icon: "🧥",
        get description() { return getText("equipable_slot_body_desc"); },
    },
    BACKPACK: {
        id: 'BACKPACK',
        get name() { return getText("equipable_slot_backpack"); },
        icon: "🎒",
        get description() { return getText("equipable_slot_backpack_desc"); },
    },
    HAND: {
        id: 'HAND',
        get name() { return getText("equipable_slot_hand"); },
        icon: "✋",
        get description() { return getText("equipable_slot_hand_desc"); },
    },
    FEET: {
        id: 'FEET',
        get name() { return getText("equipable_slot_feet"); },
        icon: "Sock",
        get description() { return getText("equipable_slot_feet_desc"); },
    }
};

const SmallBackpack = {
    id: 'SmallBackpack',
    get name() { return getText("item_small_backpack"); },
    "type": ResourceTypes.Equipable,
    "icon": "🎒",
    get description() { return getText("desc_small_backpack"); },
    "weightCap": 20,
    "slotType": EQUIPMENT_SLOT_TYPES.BACKPACK,
    ingredients: [
        { itemDef: MATERIALS.Grass, quantity: 1 },
    ],
};

const MediumBackpack = {
    id: 'MediumBackpack',
    get name() { return getText("item_medium_backpack"); },
    "type": ResourceTypes.Equipable,
    "icon": "🎒",
    get description() { return getText("desc_medium_backpack"); },
    "weightCap": 40,
    "slotType": EQUIPMENT_SLOT_TYPES.BACKPACK,
    ingredients: [
        { itemDef: SmallBackpack, quantity: 1 },
        { itemDef: MATERIALS.Grass, quantity: 1 },
    ],
};

export const BACKPACKS: { [key: string]: BackPack} = {
    "SmallBackpack": SmallBackpack,
    "MediumBackpack": MediumBackpack,
    // "large backpack": {
    //     get name() { return getText("item_large_backpack"); },
    //     "type": ResourceTypes.Equipable,
    //     "icon": "🎒",
    //     get description() { return getText("desc_large_backpack"); },
    //     "capacity": 20,
    //     "slotType": EQUIPMENT_SLOT_TYPES.BACKPACK,
    //     ingredients: [
    //         { id: 'Medium Backpack', quantity: 1 },
    //         { id: 'Grass', quantity: 8 },
    //     ],
    // },
};

export const CRAFTABLE_EQUIPABLES: { [key: string]: EquipableItem} = {
    ...BACKPACKS,
    Spear: {
        id: 'Spear',
        get name() { return getText("Spear"); },
        get description() { return getText("desc_spear"); },
        "type": ResourceTypes.Equipable,
        "slotType": EQUIPMENT_SLOT_TYPES.HAND,
        icon: '(images/spear.svg)',
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: MATERIALS.Log, quantity: 1 },
        ],
        "time": 30
    },
};

const CRAFTABLE_MATERIALS: { [key: string]: CraftableItem} = {
   "Rope": {
        id: 'Rope',
        get name() { return getText("Rope"); },
        get description() { return getText("desc_rope"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "🕸️",
        "rarity": RARITY.COMMON,
        ingredients: [
            { itemDef: MATERIALS.Vine, quantity: 1 },
        ]
    },
    "BoneGlue": {
        id: 'BoneGlue',
        get name() { return getText("Bone Glue"); },
        get description() { return getText("desc_bone_glue"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "(images/bone_glue.svg)",
        "rarity": RARITY.COMMON,
        ingredients: [
            { itemDef: MATERIALS.FishBone, quantity: 1 },
        ]
    },
};


const CRAFTABLE_TOOLS: { [key: string]: CraftableTool} = {
    ...FISHING_RODS,
    "Stone Knife": {
        id: 'Stone Knife',
        get name() { return getText("Stone Knife"); },
        get description() { return getText("desc_stone_knife"); },
        "type": ResourceTypes.TOOL,
        icon: '🔪',
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: MATERIALS.Flint, quantity: 1 },
        ],
        "durability": 100,
        "time": 30
    },
    "StoneAxe": {
        id: 'Stone Axe',
        get name() { return getText("Stone Axe"); },
        get description() { return getText("desc_stone_axe"); },
        "type": ResourceTypes.TOOL,
        icon: 'images/axe1.png', 
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: MATERIALS.Flint, quantity: 1 },
            { itemDef: MATERIALS.Log, quantity: 1 },
        ],
        "durability": 100,
        "time": 60
    },
    "Iron Axe": {
        id: 'Iron Axe',
        get name() { return getText("Iron Axe"); },
        get description() { return getText("desc_Iron_Axe"); },
        "type": ResourceTypes.TOOL,
        "icon": "🪓",
        "durability": 100,
        "rarity": RARITY.UNCOMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Steel Axe": {
        id: "Steel Axe",
        get name() { return getText("Steel Axe"); },
        get description() { return getText("desc_Steel_Axe"); },
        "type": ResourceTypes.TOOL,
        "icon": "🪓",
        "durability": 150,
        "rarity": RARITY.RARE,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Obsidian Axe": {
        id: "Obsidian Axe",
        get name() { return getText("Obsidian Axe"); },
        get description() { return getText("desc_Obsidian_Axe"); },
        "type": ResourceTypes.TOOL,
        "icon": "🪓",
        "durability": 200,
        "rarity": RARITY.EPIC,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Titanium Axe": {
        id: "Titanium Axe",
        get name() { return getText("Titanium Axe"); },
        get description() { return getText("desc_Titanium_Axe"); },
        "type": ResourceTypes.TOOL,
        "icon": "🪓",
        "durability": 250,
        "rarity": RARITY.LEGENDARY,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Stone Pickaxe": {
        id: "Stone Pickaxe",
        get name() { return getText("Stone Pickaxe"); },
        get description() { return getText("desc_Stone_Pickaxe"); },
        "type": ResourceTypes.TOOL,
        "icon": "⛏️",
        "durability": 100,
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
};

const CRAFTABLE_TRANSPORTS: { [key: string]: CraftableItem} = {
    ...Boats,
};

export const CRAFTABLES: { [key: string]: CraftableItem} = {
    ...CRAFTABLE_EQUIPABLES,
    ...CRAFTABLE_TOOLS,
    ...CRAFTABLE_MATERIALS,
    ...CRAFTABLE_TRANSPORTS,
};

export const CRAFTABLES_MAP: { [key: string]: { [key: string]: CraftableItem}} = {
    "Equipable": CRAFTABLE_EQUIPABLES,
    // "Edible": CRAFTABLE_FOODS,
    "Tool": CRAFTABLE_TOOLS,
    "Material": CRAFTABLE_MATERIALS,
    "Transport": CRAFTABLE_TRANSPORTS,
};
