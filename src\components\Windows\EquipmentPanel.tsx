import React, { useCallback } from 'react';
import { useEquipmentStore, EquipmentSlotType } from '../../stores/equipmentStore';
import { InventoryItemStack } from 'src/Interfaces';
import { InventoryItem } from '../Inventory/InventoryItem';
import { getText } from 'src/i18n';
import { CRAFTABLE_EQUIPABLES } from 'src/enums/CRAFTABLE_enums';

// Equipment slot component
interface EquipmentSlotProps {
  slotType: EquipmentSlotType;
  label: string;
  icon: string;
  equippedItem: InventoryItemStack | null;
  onUnequip: (slotType: EquipmentSlotType) => void;
}

const EquipmentSlot: React.FC<EquipmentSlotProps> = ({ 
  slotType, 
  label, 
  icon, 
  equippedItem,
  onUnequip
}) => {
  const itemDef = equippedItem ? CRAFTABLE_EQUIPABLES[equippedItem.itemId] : null;
  
  return (
    <div className={`equipment-slot ${slotType.toLowerCase()}`} data-slot={slotType}>
      {equippedItem && itemDef ? (
        <div className="equipment-item-container">
          <InventoryItem itemStack={equippedItem} itemDef={itemDef} />
          <div className="equipment-item-overlay">
            <button 
              className="unequip-button" 
              onClick={() => onUnequip(slotType)}
            >
              {getText("Unequip")}
            </button>
          </div>
        </div>
      ) : (
        <div className="empty-equipment-slot">
          <div className="slot-icon">{icon}</div>
          <div className="slot-label">{label}</div>
        </div>
      )}
    </div>
  );
};

export const EquipmentPanel: React.FC = () => {
  const { equippedItems, unequipItem } = useEquipmentStore();

  // Handle unequip button click
  const handleUnequip = useCallback((slotType: EquipmentSlotType) => {
    unequipItem(slotType);
  }, [unequipItem]);
  
  return (
      <div className="equipment-panel">
        <div className="equipment-container">
          <div className="character-silhouette">
            {/* Character silhouette image */}
            <img src="/images/character-silhouette.png" alt="Character" />
          </div>
          
          <div className="equipment-slots">
            <EquipmentSlot
              slotType="HEAD"
              label={getText("Head")}
              icon="🧢"
              equippedItem={equippedItems.HEAD}
              onUnequip={handleUnequip}
            />
            
            <EquipmentSlot
              slotType="BODY"
              label={getText("Body")}
              icon="🧥"
              equippedItem={equippedItems.BODY}
              onUnequip={handleUnequip}
            />
            
            <EquipmentSlot
              slotType="HAND"
              label={getText("Hands")}
              icon="✋"
              equippedItem={equippedItems.HAND}
              onUnequip={handleUnequip}
            />
            
            <EquipmentSlot
              slotType="FEET"
              label={getText("Feet")}
              icon="👞"
              equippedItem={equippedItems.FEET}
              onUnequip={handleUnequip}
            />
            
            <EquipmentSlot
              slotType="BACKPACK"
              label={getText("Backpack")}
              icon="🎒"
              equippedItem={equippedItems.BACKPACK}
              onUnequip={handleUnequip}
            />
          </div>
        </div>
      </div>
  );
};
