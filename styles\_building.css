

.snow-land-cell {
    background-color: rgb(251 251 251);
}

.available-buildings-header {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

#build-button {
    background-color: #4a6fa5;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 16px;
    margin: 5px;
    transition: background-color 0.3s;
}

#build-button:hover {
    background-color: #3a5a8c;
}

/* Build Window */
.build-window-content {
    /* width: 90%; */
    max-width: 1000px;
    height: 80vh;
    max-height: 800px;
}

.modal-body {
    display: flex;
    height: calc(100% - 120px);
    overflow: hidden;
}

.building-root {
    display: flex;
    flex-direction: row;
    height: 100%;
    width: 100%;
    overflow: hidden;
    justify-content: space-around;
    position: relative;
}

/* Left Panel - Available Buildings */
.build-window-left {
    /* width: 30%;
    padding: 10px;
    border-right: 1px solid #ccc;
    overflow-y: auto; */
    width: 37%;
    background: rgb(19 19 19);
    border-radius: 5px;
    padding: 10px;
    position: relative;
    transition: width 0.3s ease-in-out;
}

.build-window-left.folded {
    width: 0;
    padding: 0;
    overflow: hidden;
    margin-right: 36px; /* Space for the fold button */
}

.fold-button {
    position: absolute;
    top: 40px;
    left: 37%;
    /* right: -20px; */
    /* width: 129px; */
    height: 80px;
    background-color: rgb(31 31 31);
    color: white;
    border: none;
    border-radius: 0 3px 3px 0;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    transform: translateY(-50%);
    padding: 10px 5px;
    font-size: 14px;
    z-index: 10;
    transition: background-color 0.2s;
}

.fold-button:hover {
    background-color: rgb(40 40 40);
}

.building-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow-y: auto;
    height: 88%;
    max-height: 100%;
    padding-right: 5px;
}

.building-item {
    display: flex;
    padding: 10px;
    border: 1px solid #232323;
    border-radius: 1px;
    transition: background-color 0.2s;
}

.building-item:hover {
    background-color: #a0a0a0;
}

.building-item.selected {
    background-color: #e6f0ff;
    border-color: #4a6fa5;
}

.building-icon {
    font-size: 24px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.building-info {
    /* flex: 1; */
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.building-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.building-desc {
    font-size: 12px;
    color: #d8c2c2;
    margin-bottom: 5px;
}

.building-resources {
    font-size: 12px;
    color: #444;
}

/* Right Panel - Building Grid */
.build-window-right {
    flex: 1;
    padding: 10px;
    overflow: hidden; /* Changed from overflow-y: auto to prevent scrolling */
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 8px;
    /* width: 37%; */
}

.building-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-bottom: 0px; */
}

.grid-instructions {
    font-size: 14px;
    color: #ccc;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 5px 10px;
    border-radius: 4px;
}

.building-grid-container {
    flex: 1;
    overflow: hidden;
    position: relative;
    border-radius: 8px;
    background-color: rgba(20, 20, 20, 0.7);
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.5);
    height: 600px; /* Increased height to accommodate larger grid */
    display: flex;
    justify-content: center;
    align-items: center;
}

.building-grid {
    display: grid;
    transform-origin: center center;
    width: max-content; /* Shrink container to grid size */
    background-color: rgba(40, 40, 40, 0.7);
    border-radius: 4px;
    position: relative;
    transform-origin: center center;
    will-change: transform; /* Optimize for animations */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    margin: 'auto'; /*Center the grid in the container */
}

.grid-cell {
    /* background-color: rgba(40, 40, 40, 0.7); */
    background-color: rgb(195 214 87);
    /* background-color: rgb(3, 139, 14); */
    /* rgba(39, 255, 1, 0.7);*/
    /* border: 1px solid rgba(80, 80, 80, 0.6); */
    aspect-ratio: 1;
    transition: all 0.2s;
    position: relative;
    /* width: 20px; */
    /* height: 30px; */
}

.grid-cell:hover {
    background-color: rgba(80, 80, 80, 0.7);
    border-color: rgba(150, 150, 150, 0.8);
    /* Removed transform scale for better performance */
    z-index: 1;
}

.grid-cell.valid-placement {
    background-color: rgba(76, 175, 80, 0.3);
    border-color: #4CAF50;
}

.grid-cell.invalid-placement {
    background-color: rgba(244, 67, 54, 0.3);
    border-color: #F44336;
}

.grid-cell.selected-cell {
    border-width: 2px;
}

/* Placed building on grid */
.placed-building {
    /* background-color: rgba(255, 255, 255, 0.2); */
    /* border: 1px solid rgba(255, 255, 255, 0.4); */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    position: absolute;
    z-index: 10;
    /* pointer-events: auto; */
}

.placed-building:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.02);
}

.building-preview {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    border: 2px dashed;
    pointer-events: none;
    z-index: 5;
}

.building-preview.valid-placement {
    background-color: rgba(76, 175, 80, 0.3);
    border-color: #4CAF50;
}

.building-preview.invalid-placement {
    background-color: rgba(244, 67, 54, 0.3);
    border-color: #F44336;
}

/* Building footprint info */
.building-footprint-info {
    margin-top: 15px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.footprint-size {
    font-size: 14px;
    color: #ddd;
    margin-bottom: 5px;
}

.cancel-placement-button {
    background-color: #d7392ee7;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-left: auto;
}

.cancel-placement-button:hover {
    background-color: #ba2b2b;
}

/* World Buildings */
.world-building {
    transition: transform 0.2s;
}

.world-building:hover {
    transform: scale(1.05);
}

/* Building Interaction Menu */
.building-interaction-menu {
    position: absolute;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.interaction-button {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    text-align: left;
    transition: background-color 0.2s;
}

.interaction-button:hover {
    background-color: #e9e9e9;
}

.interaction-button.close-button {
    margin-top: 5px;
    border-top: 1px solid #eee;
    padding-top: 10px;
}