import React from "react";
import { InventoryItemStack } from "src/Interfaces";
import { StorageDragDropWrapper } from "./StorageDragDropWrapper";
import { StorageItemContent } from "./StorageItemContent";

export const StorageItemSlot = React.memo((props: {
    itemStack: InventoryItemStack | null,
    setSelectedStack: (itemStack: InventoryItemStack | null) => void,
    stackIndex: number,
    isActive: boolean,
    isSelected: boolean,
}) => {
    // Create the data object
    const draggableData = {
        index: props.stackIndex,
        itemStack: props.itemStack
    };

    // Use the StorageDragDropWrapper to isolate the DnD context changes
    return (
        <StorageDragDropWrapper
            id={`storage-${props.stackIndex}`}
            data={draggableData}
            disabled={!props.itemStack}
            isSelected={props.isSelected}
        >
            {({ ref, isDragging, isOver, attributes, listeners, isSelected }) => (
                <StorageItemContent
                    itemStack={props.itemStack}
                    isSelected={isSelected}
                    setSelectedStack={props.setSelectedStack}
                    isDragging={isDragging}
                    isOver={isOver}
                    attributes={attributes}
                    listeners={listeners}
                    innerRef={ref}
                />
            )}
        </StorageDragDropWrapper>
    );
});
