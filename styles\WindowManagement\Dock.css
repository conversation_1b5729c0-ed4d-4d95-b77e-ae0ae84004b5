.dock-container {
  position: fixed;
  bottom: 20px;
  left: 30%;
  transform: translateX(-50%);
  z-index: 1000;
  pointer-events: auto; /* Ensure dock can receive clicks */
}

.dock {
  display: flex;
  background: rgba(40, 40, 40, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(80, 80, 80, 0.3);
}

.dock-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 8px;
  position: relative;
  transition: all 0.2s ease;
}

.dock-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(60, 60, 60, 0.8);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  transition: all 0.2s ease;
}

.dock-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  filter: invert(1); /* Make SVG icons white */
}

.dock-indicator {
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #0a84ff;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(10, 132, 255, 0.6);
}

.dock-label {
  position: absolute;
  top: -36px;
  background: rgba(40, 40, 40, 0.9);
  color: white;
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
  transform: translateY(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
}

.dock-item:hover .dock-label {
  opacity: 1;
  transform: translateY(0);
}

.dock-item.active .dock-icon {
  background: rgba(10, 132, 255, 0.3);
  border: 1px solid rgba(10, 132, 255, 0.5);
  box-shadow: 0 0 10px rgba(10, 132, 255, 0.3);
}

/* Dock hover effect */
.dock-item:hover {
  transform-origin: bottom;
  transform: scale(1.1);
}

/* Dock bounce animation for minimized windows */
@keyframes dock-bounce {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

.dock-item.minimized .dock-icon {
  animation: dock-bounce 0.5s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dock {
    padding: 6px;
  }

  .dock-icon {
    width: 40px;
    height: 40px;
  }

  .dock-icon img {
    width: 24px;
    height: 24px;
  }

  .dock-item {
    margin: 0 4px;
  }
}
