import React, { useState, useCallback } from 'react';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { PlacedBuilding, InventoryItemStack, StorageBuilding } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { InventoryGrid } from './Inventory/InventoryGrid';
import { StorageGrid } from './Storage/StorageGrid';
import {
  DndContext,
  useSensor,
  useSensors,
  MouseSensor,
  TouchSensor,
  DragStartEvent,
  DragEndEvent,
  DragOverlay,
  rectIntersection,
} from "@dnd-kit/core";
import { snapCenterToCursor } from '@dnd-kit/modifiers';
import { InventoryItem } from './Inventory/InventoryItem';
import { Items } from 'src/enums/resources';

interface StorageModalProps {
  building: PlacedBuilding;
  isOpen: boolean;
  onClose: () => void;
  portalId?: string;
}

export const StorageModal: React.FC<StorageModalProps> = ({
  building,
  isOpen,
  onClose,
  portalId
}) => {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [selectedInventoryStack, setSelectedInventoryStack] = useState<InventoryItemStack | null>(null);
  const [selectedStorageStack, setSelectedStorageStack] = useState<InventoryItemStack | null>(null);

  // Get inventory and storage data from stores
  const itemStacks = useRootStore(state => state.itemStacks);
  const updateBuildingProperties = useRootStore(state => state.updateBuildingProperties);
  const setItemStacks = useRootStore(state => state.setItemStacks);

  // Initialize storage if it doesn't exist
  if (!building.storage && building.buildingDef && 'storageCapacity' in building.buildingDef) {
    const storageBuilding = building.buildingDef as StorageBuilding;
    building.storage = {
      items: [],
      capacity: storageBuilding.storageCapacity
    };
  }

  const storageItems = building.storage?.items || [];
  const storageCapacity = building.storage?.capacity || 0;

  // Convert storage items to InventoryItemStack format for display
  const storageStacks: (InventoryItemStack | null)[] = new Array(storageCapacity).fill(null);
  storageItems.forEach((item, index) => {
    if (index < storageCapacity) {
      storageStacks[index] = {
        uuid: `storage-${building.id}-${index}`,
        itemId: item.id,
        quantity: item.quantity,
        freshness: item.freshness
      };
    }
  });

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Parse IDs to determine source and destination
    const isActiveFromInventory = activeId.startsWith('draggable-');
    const isActiveFromStorage = activeId.startsWith('storage-draggable-');
    const isOverInventory = overId.startsWith('droppable-');
    const isOverStorage = overId.startsWith('storage-droppable-');

    if (isActiveFromInventory && isOverStorage) {
      // Moving from inventory to storage
      const inventoryIndex = parseInt(activeId.replace('draggable-', ''));
      const storageIndex = parseInt(overId.replace('storage-droppable-', ''));
      
      const itemStack = itemStacks[inventoryIndex];
      if (itemStack && storageIndex < storageCapacity) {
        // Remove from inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[inventoryIndex] = null;
        setItemStacks(newItemStacks);

        // Add to storage - create a sparse array to maintain slot positions
        const newStorageItems = new Array(storageCapacity).fill(null);

        // Copy existing items to their positions
        storageItems.forEach((item, index) => {
          if (index < storageCapacity && item.id) {
            newStorageItems[index] = item;
          }
        });

        // Add the new item at the target position
        newStorageItems[storageIndex] = {
          id: itemStack.itemId,
          quantity: itemStack.quantity,
          freshness: itemStack.freshness || 0
        };

        // Update building storage - filter out null items for storage
        updateBuildingProperties(building, {
          storage: {
            ...building.storage,
            items: newStorageItems.filter(item => item !== null)
          }
        });
      }
    } else if (isActiveFromStorage && isOverInventory) {
      // Moving from storage to inventory
      const storageIndex = parseInt(activeId.replace('storage-draggable-', ''));
      const inventoryIndex = parseInt(overId.replace('droppable-', ''));
      
      const storageItem = storageItems[storageIndex];
      if (storageItem && inventoryIndex < itemStacks.length) {
        // Add to inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[inventoryIndex] = {
          uuid: `moved-${Date.now()}`,
          itemId: storageItem.id,
          quantity: storageItem.quantity,
          freshness: storageItem.freshness
        };
        setItemStacks(newItemStacks);

        // Remove from storage - create a sparse array to maintain slot positions
        const newStorageItems = new Array(storageCapacity).fill(null);

        // Copy existing items to their positions, except the one being removed
        storageItems.forEach((item, index) => {
          if (index < storageCapacity && item.id && index !== storageIndex) {
            newStorageItems[index] = item;
          }
        });

        // Update building storage - filter out null items for storage
        updateBuildingProperties(building, {
          storage: {
            ...building.storage,
            items: newStorageItems.filter(item => item !== null)
          }
        });
      }
    }
  }, [itemStacks, storageItems, storageCapacity, building, setItemStacks, updateBuildingProperties]);

  // Get the currently dragged item for the overlay
  const activeItem = activeId ? (() => {
    if (activeId.startsWith('draggable-')) {
      const index = parseInt(activeId.replace('draggable-', ''));
      return itemStacks[index];
    } else if (activeId.startsWith('storage-draggable-')) {
      const index = parseInt(activeId.replace('storage-draggable-', ''));
      return storageStacks[index];
    }
    return null;
  })() : null;

  const activeItemDef = activeItem ? Items[activeItem.itemId] : null;

  return (
    <MacOSModal
      title={`${building.buildingDef.name} - ${getText('Storage')}`}
      isOpen={isOpen}
      onClose={onClose}
      initialSize={{ width: 600, height: 700 }}
      portalId={portalId}
    >
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        collisionDetection={rectIntersection}
      >
        <div className="storage-modal">
          {/* Player Inventory Section */}
          <div className="storage-section">
            <h3>{getText('Inventory')}</h3>
            <InventoryGrid
              inventorySize={30}
              itemStacks={itemStacks}
              selectedStack={selectedInventoryStack}
              setSelectedStack={setSelectedInventoryStack}
              activeId={activeId}
            />
          </div>

          {/* Storage Section */}
          <div className="storage-section">
            <h3>{`${getText('Storage')} (${storageItems.length}/${storageCapacity})`}</h3>
            <StorageGrid
              storageCapacity={storageCapacity}
              storageStacks={storageStacks}
              selectedStack={selectedStorageStack}
              setSelectedStack={setSelectedStorageStack}
              activeId={activeId}
            />
          </div>

          {/* Drag Overlay */}
          <DragOverlay modifiers={[snapCenterToCursor]}>
            {activeItem && activeItemDef ? (
              <div className="drag-overlay-item">
                <InventoryItem itemStack={activeItem} itemDef={activeItemDef} />
              </div>
            ) : null}
          </DragOverlay>
        </div>
      </DndContext>
    </MacOSModal>
  );
};
