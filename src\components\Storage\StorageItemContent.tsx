import React from "react";
import { InventoryItemStack } from "src/Interfaces";
import { InventoryItem } from "../Inventory/InventoryItem";
import { Items } from "src/enums/resources";

interface StorageItemContentProps {
    itemStack: InventoryItemStack | null;
    isSelected: boolean;
    setSelectedStack: (itemStack: InventoryItemStack | null, element?: HTMLElement) => void;
    isDragging: boolean;
    isOver: boolean;
    attributes: any;
    listeners: any;
    innerRef: React.Ref<HTMLDivElement>;
}

export const StorageItemContent = React.memo((props: StorageItemContentProps) => {
    const {
        itemStack,
        isSelected,
        setSelectedStack,
        isDragging,
        isOver,
        attributes,
        listeners,
        innerRef
    } = props;

    const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
        e.stopPropagation();
        if (itemStack) {
            setSelectedStack(itemStack, e.currentTarget);
        } else {
            setSelectedStack(null);
        }
    };

    const itemDef = itemStack ? Items[itemStack.itemId] : null;

    return (
        <div
            ref={innerRef}
            className={`storage-item-slot ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''} ${isOver ? 'drag-over' : ''}`}
            onClick={handleClick}
            {...attributes}
            {...listeners}
            style={{
                opacity: isDragging ? 0.5 : 1,
                transform: isDragging ? 'scale(1.05)' : 'scale(1)',
                transition: 'transform 0.2s ease',
                cursor: itemStack ? 'grab' : 'default'
            }}
        >
            {itemStack && itemDef ? (
                <InventoryItem itemStack={itemStack} itemDef={itemDef} />
            ) : (
                <div className="empty-storage-slot">
                    {/* Empty slot indicator */}
                </div>
            )}
        </div>
    );
});
