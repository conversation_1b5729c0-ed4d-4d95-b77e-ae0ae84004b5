import React from "react";
import { InventoryItemStack } from "src/Interfaces";
import { StorageItemSlot } from "./StorageItemSlot";

interface StorageGridProps {
    storageCapacity: number;
    storageStacks: (InventoryItemStack | null)[];
    selectedStack: InventoryItemStack | null;
    setSelectedStack: (itemStack: InventoryItemStack | null) => void;
    activeId: string | null;
}

export const StorageGrid = React.memo(({
    storageCapacity,
    storageStacks,
    selectedStack,
    setSelectedStack,
    activeId
}: StorageGridProps) => {
    return (
        <div className="storage-grid">
            {new Array(storageCapacity).fill(null).map((_, index) => (
                <StorageItemSlot
                    key={index}
                    itemStack={storageStacks[index]}
                    stackIndex={index}
                    isSelected={storageStacks[index] && selectedStack?.uuid === storageStacks[index]?.uuid}
                    setSelectedStack={setSelectedStack}
                    isActive={`storage-draggable-${index}` === activeId}
                />
            ))}
        </div>
    );
});
