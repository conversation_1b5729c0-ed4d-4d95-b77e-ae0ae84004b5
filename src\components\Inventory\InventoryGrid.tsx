import React = require("react");
import { InventoryItemStack } from "src/Interfaces";
import { InventoryItemSlot } from "./InventoryItemSlot";

interface InventoryGridProps {
    inventorySize: number;
    itemStacks: InventoryItemStack[];
    selectedStack: InventoryItemStack | null;
    setSelectedStack: (itemStack: InventoryItemStack) => void;
    activeId: string | null;
}

export const InventoryGrid = React.memo(({
    inventorySize,
    itemStacks,
    selectedStack,
    setSelectedStack,
    activeId
}: InventoryGridProps) => {
    // console.log("selectedStack ", selectedStack?.uuid === itemStacks[9]?.uuid);

    return (
        <div className="inventory-grid">
            {new Array(inventorySize).fill(null).map((_, index) => (
                <InventoryItemSlot
                    key={index}
                    itemStack={itemStacks[index]}
                    stackIndex={index}
                    isSelected={itemStacks[index] && selectedStack?.uuid === itemStacks[index].uuid}
                    setSelectedStack={setSelectedStack}
                    isActive={`draggable-${index}` === activeId}
                />
            ))}
        </div>
    );
});
