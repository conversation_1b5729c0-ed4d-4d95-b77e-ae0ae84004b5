/* React Crafting Styles */


#craftButton {
    width: 100%;
    margin-top: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: 1px solid #666;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
}

.crafting-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 10px;
    color: white;
}

#recipeList {
    /* width: 40%; */
    background: rgba(0, 0, 0, 0.92);
    /* border-radius: 5px; */
    padding: 10px;
    max-height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

#recipeListContent {
    flex: 1;
    overflow-y: auto;
    margin-top: 10px;
}

#detailsPanel {
    background: rgba(0, 0, 0, 0.93);
    border-radius: 5px;
    padding: 10px;
    width: 55%;
    max-height: 100%;
    overflow-y: auto;
}

/* .recipe-tabs {
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.recipe-tab {
    padding: 5px 10px;
    margin-right: 5px;
    cursor: pointer;
    border-radius: 3px 3px 0 0;
    background: rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.recipe-tab:hover {
    background: rgba(0, 0, 0, 0.4);
}

.recipe-tab.active {
    background: rgba(0, 0, 0, 0.5);
    border-bottom: 2px solid #4a90e2;
} */

/* .recipe-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.2s;
} */

.recipe-icon {
    font-size: 24px;
    margin-right: 10px;
}

.recipe-info {
    flex: 1;
}

.recipe-name {
    font-weight: bold;
}

.recipe-status {
    font-size: 12px;
    color: #aaa;
}

.recipe-item.available .recipe-status {
    color: #8f8;
}

.recipe-item.unavailable {
    opacity: 0.7;
}

.recipe-detail-icon {
    font-size: 32px;
    margin: 10px 0;
}

.ingredient-list {
    padding-left: 20px;
}

.has-enough {
    color: #8f8;
}

.not-enough {
    color: #f88;
}

.recipe-result {
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 5px;
}

.result-icon {
    font-size: 32px;
    margin-right: 15px;
}

.result-info {
    flex: 1;
}

.result-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.result-description {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 5px;
}

.result-stats {
    font-size: 12px;
}

